<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.admin.module.business.customer.dao.CustomerLinkDao">

    <select id="queryAnalysis" resultType="net.lab1024.sa.admin.module.business.customer.domain.vo.CustomerLinkAnalysisVO">
        WITH
        -- CTE 1: 准备基础数据，筛选出在指定日期范围内的所有相关订单
        FilteredOrders AS (
            SELECT
                平台货品ID,
                客户唯一编码,
                付款时间,
                已付 AS 付款金额
            FROM lirun.订单明细
            <where>
                <if test="queryForm.platformGoodsIds != null and queryForm.platformGoodsIds.size() > 0">
                    AND 平台货品ID IN
                    <foreach collection="queryForm.platformGoodsIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="queryForm.startDate != null and queryForm.startDate != ''">
                    AND 付款时间 &gt;= #{queryForm.startDate}
                </if>
                <if test="queryForm.endDate != null and queryForm.endDate != ''">
                    AND 付款时间 &lt; DATE_ADD(#{queryForm.endDate}, INTERVAL 1 DAY) -- 使用半开区间以利用索引
                </if>
                AND 订单状态 != '线下退款'
                AND NOT (订单来源 = '手工创建' AND 订单状态 = '已取消')
                AND 标记名称 != '贴贴贴贴贴'
                <if test="queryForm.excludeFlag != null and queryForm.excludeFlag != ''">
                    AND (客服标旗 IS NULL OR 客服标旗 NOT IN
                    <foreach collection="queryForm.excludeFlag.split(',')" item="flag" open="(" separator="," close=")">
                        #{flag}
                    </foreach>)
                </if>
            </where>
        ),

        -- CTE 1.5: 基于lirun数据库实时计算客户分类
        FilteredOrdersWithCustomerType AS (
            SELECT
                fo.平台货品ID,
                fo.客户唯一编码,
                fo.付款时间,
                fo.付款金额,
                CASE
                    WHEN EXISTS (
                        SELECT 1 FROM lirun.订单明细 hist
                        WHERE hist.客户唯一编码 = fo.客户唯一编码
                            AND hist.付款时间 &lt; #{queryForm.startDate}
                            AND hist.订单状态 != '线下退款'
                            AND NOT (hist.订单来源 = '手工创建' AND hist.订单状态 = '已取消')
                            AND hist.标记名称 != '贴贴贴贴贴'
                    ) THEN '老客'
                    ELSE '新客'
                END as 客户类型
            FROM FilteredOrders fo
            <if test="queryForm.customerType != null and queryForm.customerType != ''">
                WHERE CASE
                    WHEN EXISTS (
                        SELECT 1 FROM lirun.订单明细 hist
                        WHERE hist.客户唯一编码 = fo.客户唯一编码
                            AND hist.付款时间 &lt; #{queryForm.startDate}
                            AND hist.订单状态 != '线下退款'
                            AND NOT (hist.订单来源 = '手工创建' AND hist.订单状态 = '已取消')
                            AND hist.标记名称 != '贴贴贴贴贴'
                    ) THEN '老客'
                    ELSE '新客'
                END = #{queryForm.customerType}
            </if>
        ),

        -- CTE 2: 计算每个客户在当前时间范围内的购买统计信息
        CustomerPeriodStats AS (
            SELECT
                平台货品ID,
                客户唯一编码,
                COUNT(DISTINCT DATE(付款时间)) as 期间购买天数,
                SUM(付款金额) as 期间购买金额,
                MIN(DATE(付款时间)) as 期间首次日期,
                MAX(DATE(付款时间)) as 期间最后日期
            FROM FilteredOrdersWithCustomerType
            GROUP BY 平台货品ID, 客户唯一编码
        ),



        -- CTE 3: (V5 修正核心) 整合所有信息，进行核心的逻辑判断
        CustomerAnalysis AS (
            SELECT
                s.平台货品ID,
                s.客户唯一编码,
                s.期间购买天数,
                s.期间购买金额,
                -- 精准复购定义：无论新老客，周期内购买天数>=2才算复购
                CASE
                    WHEN s.期间购买天数 &gt;= 2 THEN 1
                    ELSE 0
                END as 是否复购客户,
                -- 复购周期只对复购客户有意义
                CASE
                    WHEN s.期间购买天数 &gt;= 2 THEN DATEDIFF(s.期间最后日期, s.期间首次日期)
                    ELSE NULL
                END as 复购周期天数
            FROM CustomerPeriodStats s
        ),

        -- CTE 4: 先提取出所有复购客户的"去重后"的购买日期，避免同一天内多次购买导致计算出0天间隔
        DistinctPurchaseDates AS (
            SELECT DISTINCT
                客户唯一编码,
                平台货品ID,
                DATE(付款时间) as purchase_date
            FROM FilteredOrdersWithCustomerType
            WHERE (客户唯一编码, 平台货品ID) IN (SELECT 客户唯一编码, 平台货品ID FROM CustomerAnalysis WHERE 是否复购客户 = 1)
        ),

        -- CTE 5: (修正) 基于去重后的日期，计算每位客户的购买时间间隔
        CustomerIntervals AS (
            SELECT
                a.平台货品ID,
                a.客户唯一编码,
                DATEDIFF(next_purchase_date, purchase_date) as interval_days
            FROM (
                SELECT
                    客户唯一编码,
                    平台货品ID,
                    purchase_date,
                    LEAD(purchase_date, 1) OVER (PARTITION BY 客户唯一编码, 平台货品ID ORDER BY purchase_date) as next_purchase_date
                FROM DistinctPurchaseDates
            ) a
            WHERE a.next_purchase_date IS NOT NULL
        ),

        -- CTE 6: (修正) 基于正确的间隔数据，计算每个客户的间隔统计指标
        IntervalStats AS (
            SELECT
                平台货品ID,
                客户唯一编码,
                MIN(interval_days) as 客户最小间隔,
                MAX(interval_days) as 客户最大间隔,
                AVG(interval_days) as 客户平均间隔
            FROM CustomerIntervals
            GROUP BY 平台货品ID, 客户唯一编码
        )

        -- 【最终步：聚合结果】
        SELECT
            COUNT(a.客户唯一编码) as paymentUserCount,
            SUM(a.是否复购客户) as repurchaseUserCount,
            ROUND(SUM(a.是否复购客户) * 100.0 / COUNT(a.客户唯一编码), 2) as repurchaseRate,
            ROUND(AVG(a.复购周期天数), 2) as avgRepurchaseCycle,
            MIN(i.客户最小间隔) as minRepurchaseInterval,
            MAX(i.客户最大间隔) as maxRepurchaseInterval,
            ROUND(AVG(i.客户平均间隔), 2) as avgRepurchaseInterval
        FROM CustomerAnalysis a
        LEFT JOIN IntervalStats i ON a.平台货品ID = i.平台货品ID AND a.客户唯一编码 = i.客户唯一编码
    </select>



    <select id="queryDetail" resultType="net.lab1024.sa.admin.module.business.customer.domain.vo.CustomerLinkDetailVO">
        -- 链接复购率次数分布分析（基于用户提供的SQL逻辑）
        -- 分析每个复购次数的人数、件数、金额分布
        WITH
        -- CTE 1: 筛选基础订单数据
        FilteredOrders AS (
            SELECT om.客户唯一编码, om.数量, om.已付, DATE(om.付款时间) as 付款日期
            FROM lirun.订单明细 om
            <where>
                om.平台货品ID IS NOT NULL AND om.平台货品ID != ''
                AND om.客户唯一编码 IS NOT NULL AND om.客户唯一编码 != ''
                AND om.付款时间 IS NOT NULL
                AND om.订单状态 != '线下退款'
                AND NOT (om.订单来源 = '手工创建' AND om.订单状态 = '已取消')
                AND om.标记名称 != '贴贴贴贴贴'
                <if test="queryForm.excludeFlag != null and queryForm.excludeFlag != ''">
                    AND (om.客服标旗 IS NULL OR om.客服标旗 NOT IN
                    <foreach collection="queryForm.excludeFlag.split(',')" item="flag" open="(" separator="," close=")">
                        #{flag}
                    </foreach>)
                </if>
                <if test="queryForm.platformGoodsIds != null and queryForm.platformGoodsIds.size() > 0">
                    AND om.平台货品ID IN
                    <foreach collection="queryForm.platformGoodsIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="queryForm.startDate != null and queryForm.startDate != ''">
                    AND om.付款时间 &gt;= #{queryForm.startDate}
                </if>
                <if test="queryForm.endDate != null and queryForm.endDate != ''">
                    AND om.付款时间 &lt; DATE_ADD(#{queryForm.endDate}, INTERVAL 1 DAY)
                </if>
            </where>
        ),

        -- CTE 1.5: 基于lirun数据库实时计算客户分类
        FilteredOrdersWithCustomerType AS (
            SELECT
                fo.客户唯一编码,
                fo.数量,
                fo.已付,
                fo.付款日期,
                CASE
                    WHEN EXISTS (
                        SELECT 1 FROM lirun.订单明细 hist
                        WHERE hist.客户唯一编码 = fo.客户唯一编码
                            AND hist.付款时间 &lt; #{queryForm.startDate}
                            AND hist.订单状态 != '线下退款'
                            AND NOT (hist.订单来源 = '手工创建' AND hist.订单状态 = '已取消')
                            AND hist.标记名称 != '贴贴贴贴贴'
                    ) THEN '老客'
                    ELSE '新客'
                END as 客户类型
            FROM FilteredOrders fo
            <if test="queryForm.customerType != null and queryForm.customerType != ''">
                WHERE CASE
                    WHEN EXISTS (
                        SELECT 1 FROM lirun.订单明细 hist
                        WHERE hist.客户唯一编码 = fo.客户唯一编码
                            AND hist.付款时间 &lt; #{queryForm.startDate}
                            AND hist.订单状态 != '线下退款'
                            AND NOT (hist.订单来源 = '手工创建' AND hist.订单状态 = '已取消')
                            AND hist.标记名称 != '贴贴贴贴贴'
                    ) THEN '老客'
                    ELSE '新客'
                END = #{queryForm.customerType}
            </if>
        ),
        -- CTE 2: 按天聚合，将同一天的所有购买合并为一次"购买事件"
        DailyAggregatedPurchases AS (
            SELECT 客户唯一编码, 付款日期, SUM(数量) as daily_quantity, SUM(已付) as daily_paid
            FROM FilteredOrdersWithCustomerType
            GROUP BY 客户唯一编码, 付款日期
        ),
        -- CTE 3: 使用窗口函数计算购买次序和上一次购买日期，用于计算时间间隔
        PurchaseSequenceWithLag AS (
            SELECT 客户唯一编码, 付款日期, daily_quantity, daily_paid,
                   ROW_NUMBER() OVER (PARTITION BY 客户唯一编码 ORDER BY 付款日期) as purchase_order,
                   LAG(付款日期, 1) OVER (PARTITION BY 客户唯一编码 ORDER BY 付款日期) as previous_purchase_date
            FROM DailyAggregatedPurchases
        ),
        -- CTE 4: 筛选出所有复购客户(总购买次数>=2)的所有购买记录，并计算出每次复购的间隔
        RepurchasersData AS (
            SELECT ps.客户唯一编码, ps.purchase_order, ps.daily_quantity, ps.daily_paid,
                   cs.total_purchases,
                   DATEDIFF(ps.付款日期, ps.previous_purchase_date) as interval_days
            FROM PurchaseSequenceWithLag ps
            JOIN (SELECT 客户唯一编码, MAX(purchase_order) as total_purchases FROM PurchaseSequenceWithLag GROUP BY 客户唯一编码) cs
              ON ps.客户唯一编码 = cs.客户唯一编码
            WHERE cs.total_purchases >= 2
        )
        -- 最终查询: 基于"复购行为"进行聚合分析
        -- 报告0: 非复购客户数据 (第0次复购)
        -- 修复说明：只包含在查询时间范围内只购买过一次的客户，排除复购客户的首次购买
        -- 这样确保第0次人数 + 第1次人数 = 总付款人数中的正确分类
        SELECT
            '第0次' as repurchaseTimes,
            COUNT(DISTINCT ps.客户唯一编码) as repurchaseCustomers,
            SUM(ps.daily_quantity) as repurchaseQuantity,
            ROUND(SUM(ps.daily_paid), 2) as repurchaseAmount,
            CASE
                WHEN COUNT(DISTINCT ps.客户唯一编码) > 0
                THEN ROUND(SUM(ps.daily_paid) / COUNT(DISTINCT ps.客户唯一编码), 2)
                ELSE NULL
            END as unitPrice,
            NULL as avgRepurchaseCycleDays
        FROM PurchaseSequenceWithLag ps
        WHERE ps.purchase_order = 1
            AND ps.客户唯一编码 NOT IN (
                SELECT 客户唯一编码
                FROM PurchaseSequenceWithLag
                GROUP BY 客户唯一编码
                HAVING MAX(purchase_order) >= 2
            )

        UNION ALL

        -- 报告1: 按"复购次数"分组的明细
        SELECT
            CONCAT('第', total_purchases - 1, '次') as repurchaseTimes,
            COUNT(DISTINCT 客户唯一编码) as repurchaseCustomers,
            SUM(CASE WHEN purchase_order > 1 THEN daily_quantity ELSE 0 END) as repurchaseQuantity,
            ROUND(SUM(CASE WHEN purchase_order > 1 THEN daily_paid ELSE 0 END), 2) as repurchaseAmount,
            CASE
                WHEN COUNT(DISTINCT 客户唯一编码) > 0
                THEN ROUND(SUM(CASE WHEN purchase_order > 1 THEN daily_paid ELSE 0 END) / COUNT(DISTINCT 客户唯一编码), 2)
                ELSE NULL
            END as unitPrice,
            ROUND(AVG(CASE WHEN purchase_order > 1 THEN interval_days ELSE NULL END), 2) as avgRepurchaseCycleDays
        FROM RepurchasersData
        GROUP BY total_purchases

        UNION ALL

        -- 报告2: 合计
        SELECT
            '合计' as repurchaseTimes,
            COUNT(DISTINCT 客户唯一编码) as repurchaseCustomers,
            SUM(CASE WHEN purchase_order > 1 THEN daily_quantity ELSE 0 END) as repurchaseQuantity,
            ROUND(SUM(CASE WHEN purchase_order > 1 THEN daily_paid ELSE 0 END), 2) as repurchaseAmount,
            CASE
                WHEN COUNT(DISTINCT 客户唯一编码) > 0
                THEN ROUND(SUM(CASE WHEN purchase_order > 1 THEN daily_paid ELSE 0 END) / COUNT(DISTINCT 客户唯一编码), 2)
                ELSE NULL
            END as unitPrice,
            -- 计算所有复购行为的平均间隔
            ROUND(AVG(interval_days), 2) as avgRepurchaseCycleDays
        FROM RepurchasersData
        WHERE purchase_order > 1

        UNION ALL

        -- 报告3: 人均
        SELECT
            '人均' as repurchaseTimes,
            NULL as repurchaseCustomers,
            ROUND(SUM(CASE WHEN purchase_order > 1 THEN daily_quantity ELSE 0 END) / COUNT(DISTINCT 客户唯一编码), 2) as repurchaseQuantity,
            ROUND(SUM(CASE WHEN purchase_order > 1 THEN daily_paid ELSE 0 END) / COUNT(DISTINCT 客户唯一编码), 2) as repurchaseAmount,
            CASE
                WHEN COUNT(DISTINCT 客户唯一编码) > 0
                THEN ROUND(SUM(CASE WHEN purchase_order > 1 THEN daily_paid ELSE 0 END) / COUNT(DISTINCT 客户唯一编码), 2)
                ELSE NULL
            END as unitPrice,
            -- 计算所有复购行为的平均间隔
            ROUND(AVG(interval_days), 2) as avgRepurchaseCycleDays
        FROM RepurchasersData
        WHERE purchase_order > 1

        ORDER BY
            CASE
                WHEN repurchaseTimes LIKE '第%次' THEN CAST(SUBSTRING(repurchaseTimes, 2, CHAR_LENGTH(repurchaseTimes) - 2) AS UNSIGNED)
                WHEN repurchaseTimes = '人均' THEN 999
                WHEN repurchaseTimes = '合计' THEN 1000
                ELSE 0
            END
    </select>

    <!-- 下载复购明细的订单明细数据 -->
    <select id="downloadOrderDetail" resultType="net.lab1024.sa.admin.module.business.customer.domain.vo.CustomerLinkOrderDetailVO">
        -- 下载明细查询（基于用户提供的SQL逻辑）
        -- 此查询用于数据核对，会列出所有底层订单
        WITH
        -- CTE 1: 筛选基础订单，与主报告逻辑一致
        FilteredOrders AS (
            SELECT om.客户唯一编码, om.数量, om.已付, DATE(om.付款时间) as 付款日期
            FROM lirun.订单明细 om
            <where>
                om.平台货品ID IS NOT NULL AND om.平台货品ID != ''
                AND om.客户唯一编码 IS NOT NULL AND om.客户唯一编码 != ''
                AND om.付款时间 IS NOT NULL
                AND om.订单状态 != '线下退款'
                AND NOT (om.订单来源 = '手工创建' AND om.订单状态 = '已取消')
                AND om.标记名称 != '贴贴贴贴贴'
                <if test="queryForm.excludeFlag != null and queryForm.excludeFlag != ''">
                    AND (om.客服标旗 IS NULL OR om.客服标旗 NOT IN
                    <foreach collection="queryForm.excludeFlag.split(',')" item="flag" open="(" separator="," close=")">
                        #{flag}
                    </foreach>)
                </if>
                <if test="queryForm.platformGoodsIds != null and queryForm.platformGoodsIds.size() > 0">
                    AND om.平台货品ID IN
                    <foreach collection="queryForm.platformGoodsIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="queryForm.startDate != null and queryForm.startDate != ''">
                    AND om.付款时间 &gt;= #{queryForm.startDate}
                </if>
                <if test="queryForm.endDate != null and queryForm.endDate != ''">
                    AND om.付款时间 &lt; DATE_ADD(#{queryForm.endDate}, INTERVAL 1 DAY)
                </if>
            </where>
        ),

        -- CTE 1.5: 基于lirun数据库实时计算客户分类
        FilteredOrdersWithCustomerType AS (
            SELECT
                fo.客户唯一编码,
                fo.数量,
                fo.已付,
                fo.付款日期,
                CASE
                    WHEN EXISTS (
                        SELECT 1 FROM lirun.订单明细 hist
                        WHERE hist.客户唯一编码 = fo.客户唯一编码
                            AND hist.付款时间 &lt; #{queryForm.startDate}
                            AND hist.订单状态 != '线下退款'
                            AND NOT (hist.订单来源 = '手工创建' AND hist.订单状态 = '已取消')
                            AND hist.标记名称 != '贴贴贴贴贴'
                    ) THEN '老客'
                    ELSE '新客'
                END as 客户类型
            FROM FilteredOrders fo
            <if test="queryForm.customerType != null and queryForm.customerType != ''">
                WHERE CASE
                    WHEN EXISTS (
                        SELECT 1 FROM lirun.订单明细 hist
                        WHERE hist.客户唯一编码 = fo.客户唯一编码
                            AND hist.付款时间 &lt; #{queryForm.startDate}
                            AND hist.订单状态 != '线下退款'
                            AND NOT (hist.订单来源 = '手工创建' AND hist.订单状态 = '已取消')
                            AND hist.标记名称 != '贴贴贴贴贴'
                    ) THEN '老客'
                    ELSE '新客'
                END = #{queryForm.customerType}
            </if>
        ),
        -- CTE 2: 按天聚合，计算每日的购买事件
        DailyAggregatedPurchases AS (
            SELECT 客户唯一编码, 付款日期
            FROM FilteredOrdersWithCustomerType GROUP BY 客户唯一编码, 付款日期
        ),
        -- CTE 3: 计算每个客户的购买次序和总购买次数
        CustomerPurchaseStats AS (
            SELECT
                客户唯一编码,
                付款日期,
                ROW_NUMBER() OVER (PARTITION BY 客户唯一编码 ORDER BY 付款日期) as purchase_order,
                COUNT(*) OVER (PARTITION BY 客户唯一编码) as total_purchases
            FROM DailyAggregatedPurchases
        )
        -- 最终查询：将分析维度关联回原始的订单明细表
        SELECT
            -- 分析维度
            CASE
                WHEN cps.purchase_order = 1 THEN '首次购买'
                ELSE '复购'
            END as repurchaseTimes,
            cps.total_purchases as purchaseOrder,
            cps.purchase_order as repurchaseCycleDays,
            -- 原始订单明细
            od.客户唯一编码 as customerUniqueCode,
            od.原始单号 as originalOrderNo,
            od.平台货品ID as platformGoodsId,
            od.商家编码 as merchantCode,
            od.数量 as quantity,
            od.已付 as paidAmount,
            od.付款时间 as paymentTime,
            od.店铺名称 as shopName
        FROM
            lirun.订单明细 od
        JOIN
            CustomerPurchaseStats cps ON od.客户唯一编码 = cps.客户唯一编码 AND DATE(od.付款时间) = cps.付款日期
        <where>
            -- 再次应用核心过滤条件，确保关联的准确性
            od.订单状态 != '线下退款'
            AND NOT (od.订单来源 = '手工创建' AND od.订单状态 = '已取消')
            AND od.标记名称 != '贴贴贴贴贴'
            <if test="queryForm.platformGoodsIds != null and queryForm.platformGoodsIds.size() > 0">
                AND od.平台货品ID IN
                <foreach collection="queryForm.platformGoodsIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="queryForm.startDate != null and queryForm.startDate != ''">
                AND od.付款时间 &gt;= #{queryForm.startDate}
            </if>
            <if test="queryForm.endDate != null and queryForm.endDate != ''">
                AND od.付款时间 &lt; DATE_ADD(#{queryForm.endDate}, INTERVAL 1 DAY)
            </if>
            <if test="queryForm.excludeFlag != null and queryForm.excludeFlag != ''">
                AND (od.客服标旗 IS NULL OR od.客服标旗 NOT IN
                <foreach collection="queryForm.excludeFlag.split(',')" item="flag" open="(" separator="," close=")">
                    #{flag}
                </foreach>)
            </if>
        </where>
        ORDER BY
            od.客户唯一编码,
            od.付款时间
    </select>

</mapper>